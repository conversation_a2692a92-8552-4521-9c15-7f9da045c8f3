import re
wiki_input = """
<wiki_structure>
  <title>DeepWiki-Open 维基</title>
  <description>DeepWiki-Open 是一个自动为 GitHub、GitLab 或 BitBucket 代码库创建美观、交互式维基的项目。</description>
  <sections>
    <section id="section-1">
      <title>概览</title>
      <pages>
        <page_ref>page-1</page_ref>
      </pages>
    </section>
    <section id="section-2">
      <title>系统架构</title>
      <pages>
        <page_ref>page-2</page_ref>
        <page_ref>page-3</page_ref>
      </pages>
    </section>
    <section id="section-3">
      <title>核心功能</title>
      <pages>
        <page_ref>page-4</page_ref>
        <page_ref>page-5</page_ref>
      </pages>
    </section>
    <section id="section-4">
      <title>数据管理与流程</title>
      <pages>
        <page_ref>page-6</page_ref>
      </pages>
    </section>
    <section id="section-5">
      <title>前端组件</title>
      <pages>
        <page_ref>page-7</page_ref>
        <page_ref>page-8</page_ref>
      </pages>
    </section>
    <section id="section-6">
      <title>后端系统</title>
      <pages>
        <page_ref>page-9</page_ref>
      </pages>
    </section>
    <section id="section-7">
      <title>模型集成</title>
      <pages>
        <page_ref>page-10</page_ref>
      </pages>
    </section>
    <section id="section-8">
      <title>部署与基础设施</title>
      <pages>
        <page_ref>page-11</page_ref>
      </pages>
    </section>
    <section id="section-9">
      <title>扩展性和定制</title>
      <pages>
        <page_ref>page-12</page_ref>
      </pages>
    </section>
  </sections>
  <pages>
    <page id="page-1">
      <title>项目简介</title>
      <description>介绍 DeepWiki-Open 的目标、功能和使用场景。</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>README.md</file_path>
      </relevant_files>
      <related_pages>
        <related>page-2</related>
      </related_pages>
      <parent_section>section-1</parent_section>
    </page>
    <page id="page-2">
      <title>架构概述</title>
      <description>描述项目的整体架构，包括前端和后端组件。</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>api/main.py</file_path>
        <file_path>src/app/layout.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>page-3</related>
      </related_pages>
      <parent_section>section-2</parent_section>
    </page>
    <page id="page-3">
      <title>组件关系</title>
      <description>展示各个组件之间的依赖关系和交互方式。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/api.py</file_path>
        <file_path>src/app/page.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>page-2</related>
      </related_pages>
      <parent_section>section-2</parent_section>
    </page>
    <page id="page-4">
      <title>文档生成</title>
      <description>解释如何使用 AI 生成文档。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/rag.py</file_path>
      </relevant_files>
      <related_pages>
        <related>page-5</related>
      </related_pages>
      <parent_section>section-3</parent_section>
    </page>
    <page id="page-5">
      <title>智能问答</title>
      <description>介绍如何使用 RAG 功能与代码库进行智能问答。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>src/components/Ask.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>page-4</related>
      </related_pages>
      <parent_section>section-3</parent_section>
    </page>
    <page id="page-6">
      <title>数据流描述</title>
      <description>说明数据如何在系统中流动和处理。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/data_pipeline.py</file_path>
      </relevant_files>
      <related_pages>
        <related>page-2</related>
      </related_pages>
      <parent_section>section-4</parent_section>
    </page>
    <page id="page-7">
      <title>主页组件</title>
      <description>描述主页的布局和功能。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>src/app/page.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>page-8</related>
      </related_pages>
      <parent_section>section-5</parent_section>
    </page>
    <page id="page-8">
      <title>仓库维基页面</title>
      <description>解释仓库维基页面的结构和内容。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>src/app/[owner]/[repo]/page.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>page-7</related>
      </related_pages>
      <parent_section>section-5</parent_section>
    </page>
    <page id="page-9">
      <title>API 服务器</title>
      <description>详细说明后端 API 服务器的实现。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/main.py</file_path>
      </relevant_files>
      <related_pages>
        <related>page-2</related>
      </related_pages>
      <parent_section>section-6</parent_section>
    </page>
    <page id="page-10">
      <title>模型集成</title>
      <description>解释如何集成不同的 AI 模型。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/openai_client.py</file_path>
      </relevant_files>
      <related_pages>
        <related>page-4</related>
      </related_pages>
      <parent_section>section-7</parent_section>
    </page>
    <page id="page-11">
      <title>部署指南</title>
      <description>提供部署项目的步骤和所需环境配置。</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>Dockerfile</file_path>
        <file_path>docker-compose.yml</file_path>
      </relevant_files>
      <related_pages>
        <related>page-2</related>
      </related_pages>
      <parent_section>section-8</parent_section>
    </page>
    <page id="page-12">
      <title>扩展性与定制</title>
      <description>说明如何扩展和定制项目功能。</description>
      <importance>low</importance>
      <relevant_files>
        <file_path>api/config/generator.json</file_path>
      </relevant_files>
      <related_pages>
        <related>page-3</related>
      </related_pages>
      <parent_section>section-9</parent_section>
    </page>
  </pages>
</wiki_structure>
"""

def parse_wiki_structure(wiki_input):
    sections_list = re.findall(r"<sections>(.*?)</sections>", wiki_input, re.DOTALL)

    print()

    return 1

def generate_single_content(wiki_input):

    language_mapping = {
        "en": "English",
        "ja": "Japanese (日本語)",
        "zh": "Mandarin Chinese (中文)",
        "zh-tw": "Traditional Chinese (繁體中文)",
        "es": "Spanish (Español)",
        "kr": "Korean (한국어)",
        "vi": "Vietnamese (Tiếng Việt)",
        "pt-br": "Brazilian Portuguese (Português Brasileiro)",
        "fr": "Français (French)",
        "ru": "Русский (Russian)"
    }

    rag_file_list = ""
    page_title = ""
    language = ""

    wiki_page_prompt = """
You are an expert technical writer and software architect.
Your task is to generate a comprehensive and accurate technical wiki page in Markdown format about a specific feature, system, or module within a given software project.

You will be given:
1. The "[WIKI_PAGE_TOPIC]" for the page you need to create.
2. A list of "[RELEVANT_SOURCE_FILES]" from the project that you MUST use as the sole basis for the content. You have access to the full content of these files. You MUST use AT LEAST 5 relevant source files for comprehensive coverage - if fewer are provided, search for additional related files in the codebase.

CRITICAL STARTING INSTRUCTION:
The very first thing on the page MUST be a `<details>` block listing ALL the `[RELEVANT_SOURCE_FILES]` you used to generate the content. There MUST be AT LEAST 5 source files listed - if fewer were provided, you MUST find additional related files to include.
Format it exactly like this:
<details>
<summary>Relevant source files</summary>

Remember, do not provide any acknowledgements, disclaimers, apologies, or any other preface before the \`<details>\` block. JUST START with the \`<details>\` block.
The following files were used as context for generating this wiki page:

{rag_file_list}
<!-- Add additional relevant files if fewer than 5 were provided -->
</details>

Immediately after the `<details>` block, the main title of the page should be a H1 Markdown heading: # {page_title}.

Based ONLY on the content of the `[RELEVANT_SOURCE_FILES]`:

1.  **Introduction:** Start with a concise introduction (1-2 paragraphs) explaining the purpose, scope, and high-level overview of "{page_title}" within the context of the overall project. If relevant, and if information is available in the provided files, link to other potential wiki pages using the format `[Link Text](#page-anchor-or-id)`.

2.  **Detailed Sections:** Break down "{page_title}" into logical sections using H2 (\`##\`) and H3 (\`###\`) Markdown headings. For each section:
    *   Explain the architecture, components, data flow, or logic relevant to the section's focus, as evidenced in the source files.
    *   Identify key functions, classes, data structures, API endpoints, or configuration elements pertinent to that section.

3.  **Mermaid Diagrams:**
    *   EXTENSIVELY use Mermaid diagrams (e.g., `flowchart TD`, `sequenceDiagram`, `classDiagram`, `erDiagram`, `graph TD`) to visually represent architectures, flows, relationships, and schemas found in the source files.
    *   Ensure diagrams are accurate and directly derived from information in the \`[RELEVANT_SOURCE_FILES]\`.
    *   Provide a brief explanation before or after each diagram to give context.
    *   CRITICAL: All diagrams MUST follow strict vertical orientation:
       - Use "graph TD" (top-down) directive for flow diagrams
       - NEVER use "graph LR" (left-right)
       - Maximum node width should be 3-4 words
       - For sequence diagrams:
         - Start with "sequenceDiagram" directive on its own line
         - Define ALL participants at the beginning
         - Use descriptive but concise participant names
         - Use the correct arrow types:
           - ->> for request/asynchronous messages
           - -->> for response messages
           - -x for failed messages
         - Include activation boxes using +/- notation
         - Add notes for clarification using "Note over" or "Note right of"

4.  **Tables:**
    *   Use Markdown tables to summarize information such as:
        *   Key features or components and their descriptions.
        *   API endpoint parameters, types, and descriptions.
        *   Configuration options, their types, and default values.
        *   Data model fields, types, constraints, and descriptions.

5.  **Code Snippets (ENTIRELY OPTIONAL):**
    *   Include short, relevant code snippets (e.g., Python, Java, JavaScript, SQL, JSON, YAML) directly from the `[RELEVANT_SOURCE_FILES]` to illustrate key implementation details, data structures, or configurations.
    *   Ensure snippets are well-formatted within Markdown code blocks with appropriate language identifiers.

6.  **Source Citations (EXTREMELY IMPORTANT):**
    *   For EVERY piece of significant information, explanation, diagram, table entry, or code snippet, you MUST cite the specific source file(s) and relevant line numbers from which the information was derived.
    *   Place citations at the end of the paragraph, under the diagram/table, or after the code snippet.
    *   Use the exact format: `Sources: [filename.ext:start_line-end_line]()` for a range, or `Sources: [filename.ext:line_number]()` for a single line. Multiple files can be cited: `Sources: [file1.ext:1-10](), [file2.ext:5](), [dir/file3.ext]()` (if the whole file is relevant and line numbers are not applicable or too broad).
    *   If an entire section is overwhelmingly based on one or two files, you can cite them under the section heading in addition to more specific citations within the section.
    *   IMPORTANT: You MUST cite AT LEAST 5 different source files throughout the wiki page to ensure comprehensive coverage.

7.  **Technical Accuracy:** All information must be derived SOLELY from the `[RELEVANT_SOURCE_FILES]`. Do not infer, invent, or use external knowledge about similar systems or common practices unless it's directly supported by the provided code. If information is not present in the provided files, do not include it or explicitly state its absence if crucial to the topic.

8.  **Clarity and Conciseness:** Use clear, professional, and concise technical language suitable for other developers working on or learning about the project. Avoid unnecessary jargon, but use correct technical terms where appropriate.

9.  **Conclusion/Summary:** End with a brief summary paragraph if appropriate for "{page_title}", reiterating the key aspects covered and their significance within the project.

IMPORTANT: Generate the content in {language} language.

Remember:
- Ground every claim in the provided source files.
- Prioritize accuracy and direct representation of the code's functionality and structure.
- Structure the document logically for easy understanding by other developers.
""".format(rag_file_list="", page_title="", language="")



if __name__ == '__main__':
    parse_wiki_structure(wiki_input)
import requests

# API endpoint
# url = "http://localhost:8001/chat/completions/stream"
url = "http://************:8001/chat/completions/stream"

# Request data
payload = {
    "repo_url": "https://github.com/AsyncFuncAI/deepwiki-open",
    "messages": [
        {
            "role": "user",
            "content": "说一下这个项目干嘛的"
        }
    ],
    "provider": "openai",
    "model": "qwen2-72b"
}

# Make streaming request
response = requests.post(url, json=payload, stream=True)

# Process the streaming response
for chunk in response.iter_content(chunk_size=None):
    if chunk:
        print(chunk.decode('utf-8'), end='', flush=True)
{"common": {"appName": "DeepWiki-Open", "tagline": "Документация с поддержкой ИИ", "generateWiki": "Создать Wiki", "processing": "Обработка...", "error": "Ошибка", "submit": "Отправить", "cancel": "Отмена", "close": "Закрыть", "loading": "Загрузка..."}, "loading": {"initializing": "Инициализация генерации wiki...", "fetchingStructure": "Получение структуры репозитория...", "determiningStructure": "Определение структуры wiki...", "clearingCache": "Очистка кеша сервера...", "preparingDownload": "Пожалуйста, подождите, идет подготовка загрузки..."}, "home": {"welcome": "Добро пожаловать в DeepWiki-Open", "welcomeTagline": "Документация с поддержкой ИИ для ваших репозиториев кода", "description": "Создавайте подробную документацию из репозиториев GitHub, GitLab или Bitbucket всего за несколько кликов.", "quickStart": "Быстрый старт", "enterRepoUrl": "Введите URL репозитория в одном из следующих форматов:", "advancedVisualization": "Продвинутая визуализация с диаграммами Mermaid", "diagramDescription": "DeepWiki автоматически генерирует интерактивные диаграммы, чтобы помочь вам понять структуру и связи в коде:", "flowDiagram": "Диаграмма потока", "sequenceDiagram": "Диаграмма последовательности"}, "form": {"repository": "Репозиторий", "configureWiki": "Настроить Wiki", "repoPlaceholder": "owner/repo или URL GitHub/GitLab/Bitbucket", "wikiLanguage": "Язык Wiki", "modelOptions": "Настройки модели", "modelProvider": "Поставщик модели", "modelSelection": "Выбор модели", "wikiType": "Тип Wiki", "comprehensive": "Подробная", "concise": "Краткая", "comprehensiveDescription": "Детализированная Wiki со структурированными разделами и большим числом страниц", "conciseDescription": "Упрощённая Wiki с меньшим числом страниц и основной информацией", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (локально)", "localOllama": "Локальная модель Ollama", "experimental": "Экспериментально", "useOpenRouter": "Использовать API OpenRouter", "openRouterModel": "Модель OpenRouter", "useOpenai": "Использовать API OpenAI", "openaiModel": "Модель OpenAI", "useCustomModel": "Использовать пользовательскую модель", "customModelPlaceholder": "Введите имя пользовательской модели", "addTokens": "+ Добавить токены доступа для приватных репозиториев", "hideTokens": "- Скрыть токены доступа", "accessToken": "Токен доступа для приватных репозиториев", "selectPlatform": "Выбрать платформу", "personalAccessToken": "Персональный токен доступа {platform}", "tokenPlaceholder": "Введите ваш токен {platform}", "tokenSecurityNote": "Токен хранится только в памяти и не сохраняется.", "defaultFiltersInfo": "Фильтры по умолчанию исключают общие директории, такие как node_modules, .git и артефакты сборки.", "fileFilterTitle": "Настройка фильтра файлов", "advancedOptions": "Дополнительные параметры", "viewDefaults": "Показать фильтры по умолчанию", "showFilters": "Показать фильтры", "hideFilters": "Скрыть фильтры", "excludedDirs": "Исключённые директории", "excludedDirsHelp": "Один путь к директории на строку. Пути, начинающиеся с ./, относительны к корню репозитория.", "enterExcludedDirs": "Введите исключённые директории, по одной на строку...", "excludedFiles": "Исключённые файлы", "excludedFilesHelp": "Один файл на строку. Поддерживаются подстановочные знаки (*).", "enterExcludedFiles": "Введите исключённые файлы, по одному на строку...", "defaultFilters": "Исключённые файлы и директории по умолчанию", "directories": "Директории", "files": "Файлы", "scrollToViewMore": "Прокрутите для просмотра", "changeModel": "Сменить модель", "defaultNote": "Эти значения уже применены. Добавьте свои исключения выше.", "hideDefault": "Скрыть по умолчанию", "viewDefault": "Показать по умолчанию", "includedDirs": "Включённые директории", "includedFiles": "Включённые файлы", "enterIncludedDirs": "Введите включённые директории, по одной на строку...", "enterIncludedFiles": "Введите включённые файлы, по одному на строку...", "filterMode": "Режим фильтрации", "excludeMode": "Исключить пути", "includeMode": "Включить только пути", "excludeModeDescription": "Укажите пути, которые нужно исключить из обработки (поведение по умолчанию)", "includeModeDescription": "Укажите только те пути, которые нужно включить, игнорируя остальные", "authorizationCode": "Код авторизации", "authorizationRequired": "Для генерации Wiki требуется авторизация."}, "footer": {"copyright": "DeepWiki — документация с поддержкой ИИ для репозиториев кода"}, "ask": {"placeholder": "Задайте вопрос об этом репозитории...", "askButton": "Спросить", "deepResearch": "Глубокое исследование", "researchInProgress": "Идёт исследование...", "continueResearch": "Продолжить исследование", "viewPlan": "Просмотреть план", "viewUpdates": "Просмотреть обновления", "viewConclusion": "Просмотреть выводы"}, "repoPage": {"refreshWiki": "Обновить Wiki", "confirmRefresh": "Подтвердить обновление", "cancel": "Отмена", "home": "Главная", "errorTitle": "Ошибка", "errorMessageDefault": "Пожалуйста, убеди<PERSON><PERSON><PERSON><PERSON>, что ваш репозиторий существует и является публичным. Допустимые форматы: \"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\" или локальные пути вроде \"C:\\\\path\\\\to\\\\folder\" или \"/path/to/folder\".", "embeddingErrorDefault": "Ошибка связана с системой встраивания документов для анализа репозитория. Проверьте конфигурацию модели встраивания, API-ключи и повторите попытку. Если проблема сохраняется, попробуйте сменить поставщика модели в настройках.", "backToHome": "Назад на главную", "exportWiki": "Экспортировать Wiki", "exportAsMarkdown": "Экспорт в Markdown", "exportAsJson": "Экспорт в JSON", "pages": "Страницы", "relatedFiles": "Связанные файлы:", "relatedPages": "Связанные страницы:", "selectPagePrompt": "Выберите страницу в навигации для просмотра её содержимого", "askAboutRepo": "Задайте вопросы об этом репозитории"}, "nav": {"wikiProjects": "Проекты Wiki"}, "projects": {"title": "Обработанные проекты Wiki", "searchPlaceholder": "Поиск проектов по названию, владельцу или репозиторию...", "noProjects": "На сервере не найдено проектов. Кеш может быть пуст или сервер столкнулся с проблемой.", "noSearchResults": "По вашему запросу проектов не найдено.", "processedOn": "Обработано:", "loadingProjects": "Загрузка проектов...", "errorLoading": "Ошибка загрузки проектов:", "backToHome": "Назад на главную", "browseExisting": "Просмотреть существующие проекты", "existingProjects": "Существующие проекты", "recentProjects": "Недавние проекты"}}